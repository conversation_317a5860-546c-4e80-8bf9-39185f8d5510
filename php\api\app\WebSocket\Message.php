<?php

namespace App\WebSocket;

use Throwable;
use SplObjectStorage;
use App\Enums\ApiCodeEnum;
use Ratchet\ConnectionInterface;
use App\Exceptions\ApiException;
use Illuminate\Support\Facades\DB;
use Ratchet\MessageComponentInterface;

class Message implements MessageComponentInterface
{
    protected $loop;
    protected $handle;
    protected $clients;
    protected $ping_interval = 25;// 25秒发送一次PING
    protected $pong_timeout = 60;// 60秒未收到PONG则断开

    public function __construct($loop)
    {
        $this->loop = $loop;
        $this->handle = new HandleMessage();
        $this->clients = new SplObjectStorage;
    }

    /**
     * 新连接建立
     *
     * @param ConnectionInterface $conn
     */
    public function onOpen(ConnectionInterface $conn)
    {
        $this->clients->attach($conn);
        $ping_timer = $this->loop->addPeriodicTimer($this->ping_interval, function () use ($conn) {
            $conn->send($this->handle->encodeMessage([
                'event' => 'ping'
            ]));
        });
        $info = (object)[
            'ping_timer' => $ping_timer,
            'pong_time' => time(),
            'user_id' => 0
        ];
        $this->clients->offsetSet($conn, $info);
    }

    /**
     * 收到消息
     *
     * @param ConnectionInterface $from
     * @param string $msg
     */
    public function onMessage(ConnectionInterface $from, $msg)
    {
        $info = $this->clients->offsetGet($from);
        $user_id = $info->user_id;
        $event = null;
        $requests = [];
        try {
            list($event, $requests) = $this->handle->getRequestData($msg, $from->resourceId, $user_id);
        } catch (Throwable $e) {
            echo "Error:" . $e->getMessage() . PHP_EOL;
        }
        if(empty($event))
        {
            return;
        }
        if($event == 'pong')
        {
            $info->pong_time = time();
            return;
        }
        $response = [
            'event' => $event,
            'code' => ApiCodeEnum::SYSTEM_ERROR,
            'message' => '发生错误',
            'data' => []
        ];
        try {
            if($event == 'server.login')
            {
                list($class, $action) = $this->handle->event2Controller($event);
                $result = (new $class)->$action($requests);
                $info->user_id = $result['data']['user']['id'];
            }else{
                if(empty($user_id))
                {
                    throw new ApiException('请登录后操作',ApiCodeEnum::UNAUTHORIZED);
                }
                list($class, $action) = $this->handle->event2Controller($event);
                $result = (new $class)->$action($user_id, $requests);
            }
            $response = array_merge(compact('event'), $result);
        } catch (Throwable $exception){
            if($exception instanceof ApiException)
            {
                $response = [
                    'event' => $event,
                    'code' => $exception->getCode(),
                    'message' => $exception->getMessage(),
                    'data' => $exception->getData()
                ];
            } else {
                DB::table('exception_logs')->insert([
                    'url' => $event,
                    'msg' => $exception->getMessage(),
                    'params' => $msg,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
        $from->send($this->handle->encodeMessage($response));
    }

    /**
     * 连接关闭
     *
     * @param ConnectionInterface $conn
     */
    public function onClose(ConnectionInterface $conn)
    {
        if ($this->clients->contains($conn))
        {
            $info = $this->clients->offsetGet($conn);
            $this->loop->cancelTimer($info->ping_timer);
            $this->clients->detach($conn);
        }
    }

    /**
     * 发生错误
     *
     * @param ConnectionInterface $conn
     * @param Throwable $e
     */
    public function onError(ConnectionInterface $conn, Throwable $e)
    {
        echo "Error:" . $e->getMessage() . PHP_EOL;
        $conn->close();
    }

    /**
     * 启动超时检测定时器
     */
    public function startTimeoutChecker()
    {
        $this->loop->addPeriodicTimer(5, function () {
            foreach ($this->clients as $client) {
                $info = $this->clients->offsetGet($client);
                if (time() - $info->pong_time > $this->pong_timeout) {
                    echo "Client timeout, closing connection\n";
                    $client->close();
                }
            }
        });
    }
}
