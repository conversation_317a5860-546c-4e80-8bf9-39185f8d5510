<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use App\Helpers\ApiTokenHelper;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;

/**
 * WebSocket Token验证服务类
 * 专门用于WebSocket业务中验证用户token是否有效
 * 为所有WebSocket业务提供统一的token验证服务
 */
class WebSocketTokenService
{
    /**
     * 验证WebSocket请求中的token是否有效
     * 
     * @param array $requests WebSocket请求数据
     * @return array 返回验证结果 ['valid' => bool, 'user_id' => int|null, 'error' => array|null]
     */
    public static function validateToken(array $requests): array
    {
        try {
            // 从请求中获取token
            $token = $requests['token'] ?? '';
            
            if (empty($token)) {
                return [
                    'valid' => false,
                    'user_id' => null,
                    'error' => [
                        'code' => ApiCodeEnum::INVALID_TOKEN,
                        'message' => 'Token不能为空'
                    ]
                ];
            }

            // 验证token格式并获取用户ID
            $user_id = ApiTokenHelper::getUserIdByToken($token);
            
            if (empty($user_id)) {
                return [
                    'valid' => false,
                    'user_id' => null,
                    'error' => [
                        'code' => ApiCodeEnum::INVALID_TOKEN,
                        'message' => 'Token格式无效'
                    ]
                ];
            }

            // 从Redis验证token是否有效
            $encrypt_token = Redis::get('user:token:' . $user_id);
            if ($encrypt_token !== ApiTokenHelper::encryptToken($token)) {
                return [
                    'valid' => false,
                    'user_id' => null,
                    'error' => [
                        'code' => ApiCodeEnum::INVALID_TOKEN,
                        'message' => 'Token已过期或无效'
                    ]
                ];
            }

            // 验证用户是否存在
            $user = DB::table('users')->select('id', 'username')->where('id', $user_id)->first();
            
            if (empty($user)) {
                return [
                    'valid' => false,
                    'user_id' => null,
                    'error' => [
                        'code' => ApiCodeEnum::USER_NOT_REGISTERED,
                        'message' => '用户不存在'
                    ]
                ];
            }

            // Token验证成功
            return [
                'valid' => true,
                'user_id' => $user_id,
                'error' => null,
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username ?? ''
                ]
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'user_id' => null,
                'error' => [
                    'code' => ApiCodeEnum::SYSTEM_ERROR,
                    'message' => 'Token验证失败: ' . $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 生成WebSocket错误响应
     * 
     * @param array $error 错误信息
     * @param string $event 事件名称
     * @return array
     */
    public static function generateErrorResponse(array $error, string $event = ''): array
    {
        return [
            'code' => $error['code'],
            'message' => $error['message'],
            'data' => [],
            'event' => $event,
            'uid' => 0
        ];
    }

    /**
     * 生成WebSocket成功响应
     * 
     * @param array $data 响应数据
     * @param string $event 事件名称
     * @param string $message 响应消息
     * @return array
     */
    public static function generateSuccessResponse(array $data = [], string $event = '', string $message = 'success'): array
    {
        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => $message,
            'data' => $data,
            'event' => $event,
            'uid' => 0
        ];
    }

    /**
     * 记录WebSocket操作日志
     * 
     * @param string $event 事件名称
     * @param int $user_id 用户ID
     * @param array $data 操作数据
     * @param string $level 日志级别 (info, error, warning)
     * @return void
     */
    public static function logWebSocketOperation(string $event, int $user_id, array $data = [], string $level = 'info'): void
    {
        $logData = [
            'event' => $event,
            'user_id' => $user_id,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        switch ($level) {
            case 'error':
                \Illuminate\Support\Facades\Log::error("WebSocket operation failed: {$event}", $logData);
                break;
            case 'warning':
                \Illuminate\Support\Facades\Log::warning("WebSocket operation warning: {$event}", $logData);
                break;
            default:
                \Illuminate\Support\Facades\Log::info("WebSocket operation: {$event}", $logData);
                break;
        }
    }
}
