<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\Publication;
use App\Models\Review;
use App\Models\ReviewAppeal;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 审核服务
 * 第3B阶段：审核系统
 */
class ReviewService
{
    /**
     * 提交审核
     */
    public function submitReview(int $userId, array $reviewData): array
    {
        try {
            DB::beginTransaction();

            // 验证发布记录
            $publication = Publication::where('id', $reviewData['publication_id'])
                ->where('user_id', $userId)
                ->first();

            if (!$publication) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '发布记录不存在',
                    'data' => []
                ];
            }

            // 检查是否已有待审核的记录
            $existingReview = Review::where('publication_id', $publication->id)
                ->where('status', Review::STATUS_PENDING)
                ->first();

            if ($existingReview) {
                return [
                    'code' => ApiCodeEnum::DUPLICATE_OPERATION,
                    'message' => '该作品已在审核队列中',
                    'data' => []
                ];
            }

            // 创建审核记录
            $review = Review::create([
                'publication_id' => $publication->id,
                'user_id' => $userId,
                'review_type' => $reviewData['review_type'],
                'status' => Review::STATUS_PENDING,
                'additional_info' => $reviewData['additional_info'] ?? null,
                'priority' => $this->calculatePriority($reviewData['review_type'], $userId),
                'metadata' => [
                    'submitted_by' => 'review_service',
                    'publication_category' => $publication->category
                ]
            ]);

            // 更新发布状态
            $publication->update([
                'status' => Publication::STATUS_PENDING_REVIEW,
                'review_status' => Publication::REVIEW_STATUS_PENDING
            ]);

            // 如果是自动审核，立即处理
            if ($reviewData['review_type'] === 'auto') {
                $this->processAutoReview($review);
            }

            DB::commit();

            // 计算队列位置和预估时间
            $queuePosition = $this->getQueuePosition($review);
            $estimatedTime = $this->getEstimatedTime($reviewData['review_type']);

            Log::info('审核提交成功', [
                'review_id' => $review->id,
                'publication_id' => $publication->id,
                'user_id' => $userId,
                'review_type' => $reviewData['review_type']
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '审核提交成功',
                'data' => [
                    'review_id' => $review->id,
                    'publication_id' => $publication->id,
                    'review_type' => $review->review_type,
                    'status' => $review->status,
                    'estimated_time' => $estimatedTime,
                    'queue_position' => $queuePosition,
                    'submitted_at' => $review->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('审核提交失败', [
                'user_id' => $userId,
                'publication_id' => $reviewData['publication_id'] ?? null,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '审核提交失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取审核状态
     */
    public function getReviewStatus(int $reviewId, int $userId): array
    {
        try {
            $review = Review::where('id', $reviewId)
                ->where('user_id', $userId)
                ->with(['reviewer:id,username'])
                ->first();

            if (!$review) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '审核记录不存在',
                    'data' => []
                ];
            }

            $data = [
                'review_id' => $review->id,
                'publication_id' => $review->publication_id,
                'status' => $review->status,
                'review_type' => $review->review_type,
                'reviewer_id' => $review->reviewer_id,
                'reviewer_name' => $review->reviewer ? $review->reviewer->username : null,
                'review_message' => $review->review_message,
                'review_score' => $review->review_score,
                'review_criteria' => $review->review_criteria,
                'submitted_at' => $review->created_at->format('Y-m-d H:i:s'),
                'reviewed_at' => $review->reviewed_at ? $review->reviewed_at->format('Y-m-d H:i:s') : null,
                'processing_time' => $review->getProcessingTime(),
                'appeal_deadline' => $review->getAppealDeadline()
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('获取审核状态失败', [
                'review_id' => $reviewId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '获取审核状态失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 提交申诉
     */
    public function submitAppeal(int $reviewId, int $userId, array $appealData): array
    {
        try {
            DB::beginTransaction();

            $review = Review::where('id', $reviewId)
                ->where('user_id', $userId)
                ->first();

            if (!$review) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '审核记录不存在',
                    'data' => []
                ];
            }

            if ($review->status !== Review::STATUS_REJECTED) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '只能对被拒绝的审核结果提出申诉',
                    'data' => []
                ];
            }

            // 检查申诉期限
            if (!$review->canAppeal()) {
                return [
                    'code' => ApiCodeEnum::EXPIRED,
                    'message' => '申诉期限已过',
                    'data' => []
                ];
            }

            // 检查申诉次数限制
            $appealCount = ReviewAppeal::where('review_id', $reviewId)->count();
            if ($appealCount >= 2) {
                return [
                    'code' => ApiCodeEnum::LIMIT_EXCEEDED,
                    'message' => '申诉次数已达上限',
                    'data' => []
                ];
            }

            // 创建申诉记录
            $appeal = ReviewAppeal::create([
                'review_id' => $reviewId,
                'user_id' => $userId,
                'appeal_reason' => $appealData['appeal_reason'],
                'additional_evidence' => $appealData['additional_evidence'] ?? null,
                'status' => ReviewAppeal::STATUS_PENDING,
                'metadata' => [
                    'appeal_count' => $appealCount + 1,
                    'original_review_score' => $review->review_score
                ]
            ]);

            // 更新审核状态
            $review->update(['status' => Review::STATUS_APPEALING]);

            DB::commit();

            Log::info('申诉提交成功', [
                'appeal_id' => $appeal->id,
                'review_id' => $reviewId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '申诉提交成功',
                'data' => [
                    'appeal_id' => $appeal->id,
                    'review_id' => $reviewId,
                    'status' => $appeal->status,
                    'appeal_reason' => $appeal->appeal_reason,
                    'estimated_time' => '3-5个工作日',
                    'submitted_at' => $appeal->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('申诉提交失败', [
                'review_id' => $reviewId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '申诉提交失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取用户审核记录
     */
    public function getUserReviews(int $userId, array $filters): array
    {
        try {
            $query = Review::where('user_id', $userId)
                ->with(['publication:id,title']);

            // 应用过滤条件
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['review_type'])) {
                $query->where('review_type', $filters['review_type']);
            }

            if (!empty($filters['date_from'])) {
                $query->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (!empty($filters['date_to'])) {
                $query->whereDate('created_at', '<=', $filters['date_to']);
            }

            // 分页
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $reviews = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $reviewList = [];
            foreach ($reviews->items() as $review) {
                $reviewList[] = [
                    'review_id' => $review->id,
                    'publication_id' => $review->publication_id,
                    'publication_title' => $review->publication ? $review->publication->title : 'N/A',
                    'status' => $review->status,
                    'review_type' => $review->review_type,
                    'review_score' => $review->review_score,
                    'submitted_at' => $review->created_at->format('Y-m-d H:i:s'),
                    'reviewed_at' => $review->reviewed_at ? $review->reviewed_at->format('Y-m-d H:i:s') : null
                ];
            }

            // 计算统计信息
            $statistics = $this->calculateUserReviewStatistics($userId);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'reviews' => $reviewList,
                    'statistics' => $statistics,
                    'pagination' => [
                        'current_page' => $reviews->currentPage(),
                        'per_page' => $reviews->perPage(),
                        'total' => $reviews->total(),
                        'last_page' => $reviews->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户审核记录失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '获取审核记录失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取队列状态
     */
    public function getQueueStatus(): array
    {
        try {
            $cacheKey = 'review_queue_status';
            
            return Cache::remember($cacheKey, 60, function () {
                $totalPending = Review::where('status', Review::STATUS_PENDING)->count();
                $autoPending = Review::where('status', Review::STATUS_PENDING)
                    ->where('review_type', 'auto')->count();
                $manualPending = Review::where('status', Review::STATUS_PENDING)
                    ->where('review_type', 'manual')->count();
                $priorityPending = Review::where('status', Review::STATUS_PENDING)
                    ->where('review_type', 'priority')->count();

                $todayReviews = Review::whereDate('created_at', Carbon::today())->count();
                $todayApproved = Review::whereDate('reviewed_at', Carbon::today())
                    ->where('status', Review::STATUS_APPROVED)->count();
                $approvalRateToday = $todayReviews > 0 ? round(($todayApproved / $todayReviews) * 100, 1) : 0;

                $averageScoreToday = Review::whereDate('reviewed_at', Carbon::today())
                    ->whereNotNull('review_score')
                    ->avg('review_score') ?? 0;

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'success',
                    'data' => [
                        'queue_info' => [
                            'total_pending' => $totalPending,
                            'auto_review_pending' => $autoPending,
                            'manual_review_pending' => $manualPending,
                            'priority_review_pending' => $priorityPending,
                            'average_wait_time' => '2.5小时',
                            'estimated_processing_time' => [
                                'auto' => '10-30分钟',
                                'manual' => '2-4小时',
                                'priority' => '30分钟-1小时'
                            ]
                        ],
                        'reviewer_status' => [
                            'online_reviewers' => 8,
                            'total_reviewers' => 12,
                            'current_load' => '中等'
                        ],
                        'recent_stats' => [
                            'reviews_today' => $todayReviews,
                            'approval_rate_today' => $approvalRateToday,
                            'average_score_today' => round($averageScoreToday, 1)
                        ]
                    ]
                ];
            });

        } catch (\Exception $e) {
            Log::error('获取队列状态失败', ['error' => $e->getMessage()]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '获取队列状态失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取审核指南
     */
    public function getReviewGuidelines(): array
    {
        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'success',
            'data' => [
                'review_criteria' => [
                    'content_quality' => [
                        'name' => '内容质量',
                        'weight' => 30,
                        'description' => '作品的创意性、完整性和表现力',
                        'scoring_guide' => [
                            'excellent' => '9-10分：创意独特，内容丰富，表现力强',
                            'good' => '7-8分：有一定创意，内容较完整',
                            'average' => '5-6分：内容基本完整，创意一般',
                            'poor' => '1-4分：内容简单，缺乏创意'
                        ]
                    ],
                    'originality' => [
                        'name' => '原创性',
                        'weight' => 25,
                        'description' => '作品的原创程度和独特性'
                    ],
                    'compliance' => [
                        'name' => '合规性',
                        'weight' => 25,
                        'description' => '是否符合平台规范和法律法规'
                    ],
                    'technical_quality' => [
                        'name' => '技术质量',
                        'weight' => 20,
                        'description' => '技术实现的质量和稳定性'
                    ]
                ],
                'prohibited_content' => [
                    '违法违规内容',
                    '色情暴力内容',
                    '侵犯他人权益',
                    '虚假误导信息',
                    '恶意营销内容'
                ],
                'best_practices' => [
                    '确保内容原创性',
                    '提供清晰的作品描述',
                    '使用准确的标签分类',
                    '保证技术质量',
                    '遵守社区规范'
                ],
                'appeal_process' => [
                    'time_limit' => '审核结果公布后7天内',
                    'required_info' => ['申诉理由', '补充证据'],
                    'processing_time' => '3-5个工作日',
                    'appeal_limit' => '每个作品最多申诉2次'
                ]
            ]
        ];
    }

    /**
     * 快速预检
     */
    public function performPreCheck(int $userId, array $checkData): array
    {
        try {
            // 简化的预检逻辑
            $score = 85; // 基础分数
            $riskLevel = 'low';
            $suggestions = [];
            $potentialIssues = [];

            // 检查标题
            if (strlen($checkData['title']) < 5) {
                $score -= 10;
                $suggestions[] = '建议标题更加详细，至少5个字符';
            }

            // 检查描述
            if (empty($checkData['description']) || strlen($checkData['description']) < 20) {
                $score -= 5;
                $suggestions[] = '建议添加详细的作品描述，有助于用户理解';
            }

            // 检查标签
            if (empty($checkData['tags']) || count($checkData['tags']) < 2) {
                $score -= 5;
                $suggestions[] = '建议添加更多相关标签，提高作品可发现性';
            }

            // 根据分数确定风险级别
            if ($score >= 80) {
                $riskLevel = 'low';
            } elseif ($score >= 60) {
                $riskLevel = 'medium';
            } else {
                $riskLevel = 'high';
            }

            $estimatedApprovalRate = max(50, min(95, $score));
            $recommendedReviewType = $score >= 80 ? 'auto' : 'manual';

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '预检完成',
                'data' => [
                    'pre_check_id' => time(),
                    'overall_score' => $score,
                    'risk_level' => $riskLevel,
                    'estimated_approval_rate' => $estimatedApprovalRate,
                    'suggestions' => $suggestions,
                    'potential_issues' => $potentialIssues,
                    'recommended_review_type' => $recommendedReviewType,
                    'estimated_review_time' => $recommendedReviewType === 'auto' ? '10-30分钟' : '2-4小时'
                ]
            ];

        } catch (\Exception $e) {
            Log::error('预检失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '预检失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    // 私有辅助方法
    private function calculatePriority(string $reviewType, int $userId): int
    {
        $priority = 5; // 默认优先级

        switch ($reviewType) {
            case 'priority':
                $priority = 1;
                break;
            case 'auto':
                $priority = 3;
                break;
            case 'manual':
                $priority = 5;
                break;
        }

        return $priority;
    }

    private function getQueuePosition(Review $review): int
    {
        return Review::where('status', Review::STATUS_PENDING)
            ->where('priority', '<=', $review->priority)
            ->where('created_at', '<', $review->created_at)
            ->count() + 1;
    }

    private function getEstimatedTime(string $reviewType): string
    {
        $timeMap = [
            'auto' => '10-30分钟',
            'manual' => '2-4小时',
            'priority' => '30分钟-1小时'
        ];

        return $timeMap[$reviewType] ?? '2-4小时';
    }

    private function processAutoReview(Review $review): void
    {
        // 简化的自动审核逻辑
        try {
            $score = rand(75, 95); // 模拟自动评分
            $status = $score >= 80 ? Review::STATUS_APPROVED : Review::STATUS_REJECTED;

            $review->update([
                'status' => $status,
                'review_score' => $score,
                'review_message' => $status === Review::STATUS_APPROVED 
                    ? '自动审核通过，作品质量良好' 
                    : '自动审核未通过，建议人工复审',
                'reviewer_id' => null, // 自动审核无审核员
                'reviewed_at' => Carbon::now(),
                'review_criteria' => [
                    'content_quality' => rand(7, 10),
                    'originality' => rand(7, 10),
                    'compliance' => rand(8, 10),
                    'technical_quality' => rand(7, 9)
                ]
            ]);

            // 更新发布状态
            $publication = Publication::find($review->publication_id);
            if ($publication) {
                $publication->update([
                    'status' => $status === Review::STATUS_APPROVED 
                        ? Publication::STATUS_PUBLISHED 
                        : Publication::STATUS_REJECTED,
                    'review_status' => $status === Review::STATUS_APPROVED 
                        ? Publication::REVIEW_STATUS_APPROVED 
                        : Publication::REVIEW_STATUS_REJECTED,
                    'published_at' => $status === Review::STATUS_APPROVED ? Carbon::now() : null
                ]);
            }

        } catch (\Exception $e) {
            Log::error('自动审核处理失败', [
                'review_id' => $review->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function calculateUserReviewStatistics(int $userId): array
    {
        $reviews = Review::where('user_id', $userId)->get();

        $totalReviews = $reviews->count();
        $approvedCount = $reviews->where('status', Review::STATUS_APPROVED)->count();
        $rejectedCount = $reviews->where('status', Review::STATUS_REJECTED)->count();
        $pendingCount = $reviews->where('status', Review::STATUS_PENDING)->count();

        $averageScore = $reviews->whereNotNull('review_score')->avg('review_score') ?? 0;
        $approvalRate = $totalReviews > 0 ? round(($approvedCount / $totalReviews) * 100, 1) : 0;

        return [
            'total_reviews' => $totalReviews,
            'approved_count' => $approvedCount,
            'rejected_count' => $rejectedCount,
            'pending_count' => $pendingCount,
            'average_score' => round($averageScore, 1),
            'approval_rate' => $approvalRate
        ];
    }
}
