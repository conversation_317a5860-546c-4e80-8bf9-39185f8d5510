<?php

namespace App\Console\Commands;

use Throwable;
use Swoole\Table;
use Swoole\Timer;
use App\Enums\ApiCodeEnum;
use App\Exceptions\ApiException;
use Swoole\WebSocket\Server;
use Illuminate\Console\Command;
use App\WebSocket\HandleMessage;
use App\Services\WebSocketService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class WebSocketServer extends Command
{
    /**
     * 命令名称
     * 支持两种模式：传统模式和现代会话模式
     * 执行命令 : php artisan websocket:serve
     * @var string
     */
    protected $signature = 'websocket:serve {--port=} {--host=} {--mode=}';

    /**
     * 命令描述
     * @var string
     */
    protected $description = '启动WebSocket服务 (支持传统模式和现代会话模式)';

    protected $webSocketService;

    public function __construct(WebSocketService $webSocketService)
    {
        parent::__construct();
        $this->webSocketService = $webSocketService;
    }

    public function handle()
    {
        $mode = $this->option('mode') ?: config('websocket.server.mode');
        $host = $this->option('host') ?: config('websocket.server.host');
        $port = $this->option('port') ?: config('websocket.server.port');

        $this->info("Starting WebSocket server on {$host}:{$port} in {$mode} mode");

        // 检查Swoole扩展
        if (!extension_loaded('swoole')) {
            $this->error('Swoole extension is not installed. Please install swoole extension first.');
            return 1;
        }

        if ($mode === 'modern') {
            return $this->handleModernMode($host, $port);
        } else {
            return $this->handleLegacyMode($host, $port);
        }
    }

    /**
     * 现代会话模式 (来自WebSocketServerCommand)
     */
    private function handleModernMode($host, $port)
    {
        try {
            // 记录服务器启动时间
            Cache::put('websocket_server_start_time', Carbon::now(), 86400);

            // 创建WebSocket服务器
            /** @var mixed $server WebSocket服务器实例 */
            $server = new \Swoole\WebSocket\Server($host, $port);

            // 配置服务器
            $server->set([
                'worker_num' => config('websocket.performance.worker_num'),
                'heartbeat_check_interval' => config('websocket.performance.heartbeat_check_interval'),
                'heartbeat_idle_time' => config('websocket.performance.heartbeat_idle_time'),
                'max_connection' => config('websocket.performance.max_connection'),
                'package_max_length' => config('websocket.performance.package_max_length'),
                'enable_coroutine' => true,
                'log_file' => storage_path('logs/' . config('websocket.logging.file')),
                'log_level' => 4, // SWOOLE_LOG_INFO
            ]);

            // 监听连接打开事件
            $server->on('open', function ($server, $request) {
                $this->handleModernOpen($server, $request);
            });

            // 监听消息事件
            $server->on('message', function ($server, $frame) {
                $this->handleModernMessage($server, $frame);
            });

            // 监听连接关闭事件
            $server->on('close', function ($server, $fd) {
                $this->handleModernClose($server, $fd);
            });

            $this->info("WebSocket server started successfully in modern mode!");
            $this->info("Listening on wss://{$host}:{$port}");

            // 启动服务器
            $server->start();

        } catch (\Exception $e) {
            $this->error("Failed to start WebSocket server: " . $e->getMessage());
            Log::error('WebSocket服务器启动失败', ['error' => $e->getMessage()]);
            return 1;
        }
    }

    /**
     * 传统模式 (原有的SSL模式)
     */
    private function handleLegacyMode($host, $port)
    {
        // ======================= SSL配置 - 开始 =======================
        // 从配置文件获取SSL证书路径
        $ssl_cert_file = config('websocket.ssl.cert_file');
        $ssl_key_file = config('websocket.ssl.key_file');

        // 如果配置文件未配置，使用默认路径
        if (!$ssl_cert_file || !$ssl_key_file) {
            $basePath = __DIR__ . '/ssl';
            $ssl_cert_file = $basePath.'/api.tiptop.cn.pem';
            $ssl_key_file = $basePath.'/api.tiptop.cn.key';
        } else {
            // 检查是否为绝对路径，如果不是则转换为绝对路径
            if (!$this->isAbsolutePath($ssl_cert_file)) {
                $ssl_cert_file = base_path($ssl_cert_file);
            }
            if (!$this->isAbsolutePath($ssl_key_file)) {
                $ssl_key_file = base_path($ssl_key_file);
            }
        }

        // 检查证书文件是否存在，如果不存在则提示错误并退出
        if (!file_exists($ssl_cert_file) || !file_exists($ssl_key_file)) {
            $this->error("SSL certificate files not found!");
            $this->error("Cert file path: " . $ssl_cert_file);
            $this->error("Key file path:  " . $ssl_key_file);
            $this->info("Please configure SSL certificate paths in config/websocket.php file:");
            $this->info("'ssl' => [");
            $this->info("    'cert_file' => 'path/to/certificate.pem',");
            $this->info("    'key_file' => 'path/to/private.key',");
            $this->info("],");
            $this->info("Or use 'mkcert api.tiptop.cn' to generate them in the default location");
            return 1;
        }

        // 创建服务器时，第四个参数需要增加 SWOOLE_SSL 来启用加密
        /** @var mixed $server SSL WebSocket服务器实例 */
        $server = new Server(
            $host,
            $port,
            3, // SWOOLE_PROCESS
            1 | 512 // SWOOLE_SOCK_TCP | SWOOLE_SSL
        );
        
        $server->set([
            // 添加SSL证书和密钥文件配置
            'ssl_cert_file' => $ssl_cert_file,
            'ssl_key_file'  => $ssl_key_file,

            // 你原来的配置
            'reload_async' => true,
            'max_wait_time' => 60,
            'open_eof_check' => true,
            'package_eof' => '}',
            'heartbeat_check_interval' => 25,// 心跳间隔，单位秒
            'heartbeat_idle_time' => 60,// 空闲时间，单位秒
        ]);
        // ======================= SSL配置 - 结束 =======================

        /** @var mixed $table Swoole内存表实例 */
        $table = new Table(1024 * 100);
        $table->column('user_id', Table::TYPE_INT);
        $table->column('ping_at', Table::TYPE_INT);
        $table->create();

        /** @var mixed $user_table 用户内存表实例 */
        $user_table = new Table(1024 * 100);
        $user_table->column('fd', Table::TYPE_INT);
        $user_table->create();

        $handle = new HandleMessage();

        $this->info("WebSocket Server starting with SSL enabled in legacy mode...");
        $this->info("Listening on wss://{$host}:{$port}");

        $server->on('workerstart', function ($server, $worker_id) use($table, $handle){
            if ($worker_id == 0) // 仅在worker0进程启动定时器
            {
                // 启动定时器进行心跳检测
                Timer::tick(25000, function () use ($server, $table, $handle){
                    foreach ($server->connections as $fd)
                    {
                        if($server->exist($fd) && $server->isEstablished($fd))
                        {
                            $ping_at = $table->get($fd, 'ping_at');
                            if(!empty($ping_at) && time() - $ping_at >= 25){
                                $server->push($fd, $handle->encodeMessage([
                                    'event' => 'ping'
                                ]));
                                $table->set($fd, ['ping_at' => time()]);
                            }
                        }
                    }
                });
            }
        });

        $server->on('open', function ($server, $request) use ($table, $handle){
            $table->set($request->fd, ['ping_at' => time()]);
            $this->info("Connection open: #" . $request->fd);
        });

        $server->on('message', function ($server, $frame) use ($table, $user_table, $handle) {
            $this->info("Received from #" . $frame->fd . ": " . $frame->data);
            $event = '';
            $user_id = 0;
            $requests = [];
            $response = ['code' => ApiCodeEnum::SYSTEM_ERROR, 'message' => '发生错误', 'data' => []];
            try {
                $user_id = $table->get($frame->fd, 'user_id');
                list($event, $requests) = $handle->getRequestData($frame->data, $frame->fd);
                if(empty($event) || $event == 'pong')
                {
                    return false;
                }
                // 所有WebSocket事件都直接调用对应的控制器，由控制器内部验证token
                list($class, $action) = $handle->event2Controller($event);
                $response = (new $class)->$action($requests);
            }
            catch (Throwable $e)
            {
                \Illuminate\Support\Facades\Log::error($e->getMessage().PHP_EOL.'file:'.$e->getFile().PHP_EOL.'line:'.$e->getLine());
                if($e instanceof ApiException)
                {
                    $response = ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => $e->getData()];
                } else {
                    $url = $event;
                    if(!empty($user_id))
                    {
                        $url .= ' @ ' . $user_id;
                    }
                    // 直接记录错误日志而不是使用队列
                    \Illuminate\Support\Facades\Log::error('WebSocket error', [
                        'url' => $url,
                        'message' => $e->getMessage(),
                        'params' => $frame->data,
                        'user_id' => $user_id
                    ]);
                    $response = ['code' => 500, 'message' => '发生错误', 'data' => []];
                }
            }
            $response['event'] = $event;
            $response['uid'] = $requests['uid'] ?? 0;
            $this->info("Push to #" . $frame->fd . ": " . json_encode($response));
            $server->push($frame->fd, $handle->encodeMessage($response));
        });

        $server->on('close', function ($server, $fd) use ($table, $user_table, $handle) {
            $this->info('Connection close: #' . $fd);
            if($table->exist($fd))
            {
                $user_id = $table->get($fd, 'user_id');
                $table->delete($fd);
                if(!empty($user_id))
                {
                    $flag = true;
                    $user_fd = $user_table->get($user_id, 'fd');
                    if(!empty($user_fd))
                    {
                        if($user_fd == $fd)
                        {
                            $user_table->delete($user_id);
                        } else {
                            $flag = false;
                        }
                    }
                    if ($flag) {
                        // 直接记录用户离线日志而不是使用队列
                        \Illuminate\Support\Facades\Log::info('User offline', ['user_id' => $user_id]);
                    }
                }
            }
        });

        $server->start();
    }

    /**
     * 现代模式：处理连接打开
     */
    private function handleModernOpen($server, $request)
    {
        $fd = $request->fd;
        $sessionId = $request->get['session_id'] ?? null;

        if (!$sessionId) {
            $server->close($fd);
            return;
        }

        // 验证会话ID
        $session = \App\Models\WebSocketSession::where('session_id', $sessionId)
            ->where('status', 'connected')
            ->first();

        if (!$session) {
            $server->close($fd);
            return;
        }

        // 存储连接映射
        Cache::put("websocket_fd_{$fd}", $sessionId, 3600);
        Cache::put("websocket_session_{$sessionId}", $fd, 3600);

        $this->info("Client connected: fd={$fd}, session={$sessionId}");
        Log::info('WebSocket客户端连接', [
            'fd' => $fd,
            'session_id' => $sessionId,
            'user_id' => $session->user_id
        ]);

        // 发送连接成功消息
        $server->push($fd, json_encode([
            'event' => 'connected',
            'data' => [
                'session_id' => $sessionId,
                'server_time' => Carbon::now()->toISOString()
            ]
        ]));
    }

    /**
     * 现代模式：处理消息
     */
    private function handleModernMessage($server, $frame)
    {
        $fd = $frame->fd;
        $data = $frame->data;

        try {
            $message = json_decode($data, true);
            if (!$message) {
                return;
            }

            $sessionId = Cache::get("websocket_fd_{$fd}");
            if (!$sessionId) {
                $server->close($fd);
                return;
            }

            $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            if (!$session) {
                $server->close($fd);
                return;
            }

            // 处理不同类型的消息
            switch ($message['type'] ?? '') {
                case 'ping':
                    $this->handleModernPing($server, $fd, $session);
                    break;

                case 'subscribe':
                    $this->handleModernSubscribe($server, $fd, $session, $message['events'] ?? []);
                    break;

                case 'unsubscribe':
                    $this->handleModernUnsubscribe($server, $fd, $session, $message['events'] ?? []);
                    break;

                default:
                    // 未知消息类型
                    break;
            }

        } catch (\Exception $e) {
            Log::error('WebSocket消息处理失败', [
                'fd' => $fd,
                'data' => $data,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 现代模式：处理连接关闭
     */
    private function handleModernClose($server, $fd)
    {
        $sessionId = Cache::get("websocket_fd_{$fd}");

        if ($sessionId) {
            $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            if ($session) {
                $session->disconnect('客户端断开连接');
            }

            // 清理缓存
            Cache::forget("websocket_fd_{$fd}");
            Cache::forget("websocket_session_{$sessionId}");
        }

        $this->info("Client disconnected: fd={$fd}");
        Log::info('WebSocket客户端断开', ['fd' => $fd, 'session_id' => $sessionId]);
    }

    /**
     * 现代模式：处理心跳
     */
    private function handleModernPing($server, $fd, $session)
    {
        $session->updatePing();

        $server->push($fd, json_encode([
            'event' => 'pong',
            'data' => [
                'server_time' => Carbon::now()->toISOString()
            ]
        ]));
    }

    /**
     * 现代模式：处理订阅事件
     */
    private function handleModernSubscribe($server, $fd, $session, $events)
    {
        foreach ($events as $event) {
            $session->subscribeEvent($event);
        }

        $server->push($fd, json_encode([
            'event' => 'subscribed',
            'data' => [
                'events' => $events,
                'subscribed_events' => $session->subscribed_events
            ]
        ]));
    }

    /**
     * 现代模式：处理取消订阅事件
     */
    private function handleModernUnsubscribe($server, $fd, $session, $events)
    {
        foreach ($events as $event) {
            $session->unsubscribeEvent($event);
        }

        $server->push($fd, json_encode([
            'event' => 'unsubscribed',
            'data' => [
                'events' => $events,
                'subscribed_events' => $session->subscribed_events
            ]
        ]));
    }

    /**
     * 检查路径是否为绝对路径
     */
    private function isAbsolutePath($path)
    {
        // Windows: 检查是否以盘符开头 (如 C:\ 或 D:\)
        if (PHP_OS_FAMILY === 'Windows' || strpos(PHP_OS, 'WIN') !== false) {
            return preg_match('/^[a-zA-Z]:[\\\\\/]/', $path);
        }
        // Unix/Linux: 检查是否以 / 开头
        return strpos($path, '/') === 0;
    }
}