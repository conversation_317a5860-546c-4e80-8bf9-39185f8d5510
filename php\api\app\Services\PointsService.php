<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use App\Models\User;
use App\Models\PointsTransaction;
use App\Models\PointsFreeze;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 积分管理服务
 */
class PointsService
{
    /**
     * 积分充值
     */
    public function recharge(int $userId, float $amount, string $paymentMethod): array
    {
        try {
            DB::beginTransaction();

            // 使用DB facade查询用户，避免Eloquent连接问题
            $user = DB::table('users')->where('id', $userId)->first();
            if (!$user) {
                throw new \Exception('用户不存在');
            }
            
            // 创建充值交易记录
            $transactionId = DB::table('points_transactions')->insertGetId([
                'user_id' => $userId,
                'business_type' => 'recharge',
                'business_id' => 'recharge_' . time() . '_' . $userId,
                'amount' => $amount,
                'status' => 'success', // PointsTransaction::STATUS_SUCCESS
                'ai_platform' => null,
                'request_data' => json_encode([
                    'payment_method' => $paymentMethod,
                    'original_amount' => $amount
                ]),
                'response_data' => json_encode([
                    'payment_status' => 'success',
                    'payment_time' => Carbon::now()->toISOString()
                ]),
                'completed_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            // 增加用户积分
            $newPoints = $user->points + $amount;
            DB::table('users')->where('id', $userId)->update(['points' => $newPoints]);

            DB::commit();

            Log::info('用户积分充值成功', [
                'user_id' => $userId,
                'amount' => $amount,
                'payment_method' => $paymentMethod,
                'transaction_id' => $transactionId,
                'new_balance' => $newPoints
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '充值成功',
                'data' => [
                    'transaction_id' => $transactionId,
                    'amount' => $amount,
                    'new_balance' => $newPoints
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('用户积分充值失败', [
                'user_id' => $userId,
                'amount' => $amount,
                'payment_method' => $paymentMethod,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '充值失败，请稍后重试',
                'data' => []
            ];
        }
    }

    /**
     * 冻结积分
     */
    public function freezePoints(int $userId, float $amount, string $businessType, string $businessId = null, int $timeoutSeconds = 300): array
    {
        try {
            // 验证业务类型有效性
            if (!$this->isValidBusinessType($businessType)) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '无效的业务类型',
                    'data' => []
                ];
            }

            DB::beginTransaction();

            $user = User::findOrFail($userId);
            
            // 检查积分是否足够
            if (!$user->hasEnoughPoints($amount)) {
                return [
                    'code' => ApiCodeEnum::INSUFFICIENT_POINTS,
                    'message' => '积分不足',
                    'data' => [
                        'required' => $amount,
                        'available' => $user->points
                    ]
                ];
            }

            // 创建积分交易记录
            $transaction = PointsTransaction::create([
                'user_id' => $userId,
                'business_type' => $businessType,
                'business_id' => $businessId,
                'amount' => $amount,
                'status' => PointsTransaction::STATUS_FROZEN,
                'timeout_seconds' => $timeoutSeconds
            ]);

            // 创建积分冻结记录
            $freeze = PointsFreeze::create([
                'user_id' => $userId,
                'transaction_id' => $transaction->id,
                'amount' => $amount,
                'status' => PointsFreeze::STATUS_FROZEN,
                'business_type' => $businessType,
                'business_id' => $businessId,
                'expires_at' => Carbon::now()->addSeconds($timeoutSeconds),
                'reason' => "业务类型：{$businessType}，冻结积分：{$amount}"
            ]);

            // 冻结用户积分
            $user->freezePoints($amount, $businessType, $businessId);

            DB::commit();

            Log::info('积分冻结成功', [
                'user_id' => $userId,
                'amount' => $amount,
                'business_type' => $businessType,
                'business_id' => $businessId,
                'transaction_id' => $transaction->id,
                'freeze_id' => $freeze->id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分冻结成功',
                'data' => [
                    'transaction_id' => $transaction->id,
                    'freeze_id' => $freeze->id,
                    'amount' => $amount,
                    'expires_at' => $freeze->expires_at->toISOString()
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('积分冻结失败', [
                'user_id' => $userId,
                'amount' => $amount,
                'business_type' => $businessType,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '积分冻结失败',
                'data' => []
            ];
        }
    }

    /**
     * 释放冻结积分
     */
    public function releasePoints(int $transactionId): array
    {
        try {
            DB::beginTransaction();

            $transaction = PointsTransaction::findOrFail($transactionId);
            $freeze = PointsFreeze::where('transaction_id', $transactionId)->first();
            
            if (!$freeze || $freeze->status !== PointsFreeze::STATUS_FROZEN) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '无效的冻结记录',
                    'data' => []
                ];
            }

            $user = User::findOrFail($transaction->user_id);

            // 释放冻结积分
            $user->releasePoints($freeze->amount);
            $freeze->release();
            
            // 更新交易状态
            $transaction->status = PointsTransaction::STATUS_FAILED;
            $transaction->completed_at = Carbon::now();
            $transaction->failure_reason = '积分已释放';
            $transaction->save();

            DB::commit();

            Log::info('积分释放成功', [
                'transaction_id' => $transactionId,
                'user_id' => $user->id,
                'amount' => $freeze->amount
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分释放成功',
                'data' => [
                    'amount' => $freeze->amount,
                    'new_balance' => $user->points
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('积分释放失败', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '积分释放失败',
                'data' => []
            ];
        }
    }

    /**
     * 消费冻结积分
     */
    public function consumePoints(int $transactionId): array
    {
        try {
            DB::beginTransaction();

            $transaction = PointsTransaction::findOrFail($transactionId);
            $freeze = PointsFreeze::where('transaction_id', $transactionId)->first();
            
            if (!$freeze || $freeze->status !== PointsFreeze::STATUS_FROZEN) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '无效的冻结记录',
                    'data' => []
                ];
            }

            $user = User::findOrFail($transaction->user_id);

            // 消费冻结积分
            $user->consumeFrozenPoints($freeze->amount);
            $freeze->consume();
            
            // 更新交易状态
            $transaction->status = PointsTransaction::STATUS_SUCCESS;
            $transaction->completed_at = Carbon::now();
            $transaction->save();

            DB::commit();

            Log::info('积分消费成功', [
                'transaction_id' => $transactionId,
                'user_id' => $user->id,
                'amount' => $freeze->amount
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分消费成功',
                'data' => [
                    'amount' => $freeze->amount,
                    'remaining_points' => $user->points,
                    'remaining_frozen_points' => $user->frozen_points
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('积分消费失败', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '积分消费失败',
                'data' => []
            ];
        }
    }

    /**
     * 处理超时的冻结积分
     */
    public function handleTimeoutTransactions(): int
    {
        $timeoutTransactions = PointsTransaction::timeout()->get();
        $processedCount = 0;

        foreach ($timeoutTransactions as $transaction) {
            $result = $this->releasePoints($transaction->id);
            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                $processedCount++;
            }
        }

        Log::info('处理超时积分冻结', [
            'total_timeout' => $timeoutTransactions->count(),
            'processed' => $processedCount
        ]);

        return $processedCount;
    }

    /**
     * 验证业务类型是否有效
     */
    private function isValidBusinessType(string $businessType): bool
    {
        $validTypes = [
            PointsTransaction::TYPE_TEXT_TO_IMAGE,
            PointsTransaction::TYPE_IMAGE_TO_VIDEO,
            PointsTransaction::TYPE_TEXT_GENERATION,
            PointsTransaction::TYPE_VOICE_SYNTHESIS,
            // 添加其他支持的业务类型
            'image_generation',
            'video_generation',
            'music_generation',
            'sound_generation',
            'story_generation',
            'character_generation',
            // 任务管理相关业务类型
            'task_cancel',
            'task_refund',
            'recharge'
        ];

        return in_array($businessType, $validTypes);
    }

    /**
     * 检查积分是否充足
     * 第2E阶段：新增方法
     */
    public function checkPoints(int $userId, float $amount, string $businessType, ?int $businessId = null): array
    {
        try {
            // 验证业务类型有效性
            if (!$this->isValidBusinessType($businessType)) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '无效的业务类型',
                    'data' => []
                ];
            }

            $user = User::findOrFail($userId);

            $sufficient = $user->points >= $amount;
            $shortage = $sufficient ? 0 : ($amount - $user->points);

            Log::info('积分检查', [
                'user_id' => $userId,
                'required_amount' => $amount,
                'current_balance' => $user->points,
                'sufficient' => $sufficient,
                'business_type' => $businessType,
                'business_id' => $businessId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分检查完成',
                'data' => [
                    'sufficient' => $sufficient,
                    'current_balance' => number_format($user->points, 4),
                    'required_amount' => number_format($amount, 4),
                    'shortage' => number_format($shortage, 4)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('积分检查失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '积分检查失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 确认积分使用
     * 第2D1阶段：新增方法
     */
    public function confirmPointsUsage(int $userId, float $amount, string $businessType, int $businessId): array
    {
        try {
            DB::beginTransaction();

            // 创建积分消费记录
            $transaction = PointsTransaction::create([
                'user_id' => $userId,
                'business_type' => $businessType,
                'business_id' => $businessId,
                'amount' => -$amount,
                'status' => PointsTransaction::STATUS_SUCCESS,
                'completed_at' => Carbon::now()
            ]);

            DB::commit();

            Log::info('积分使用确认成功', [
                'user_id' => $userId,
                'amount' => $amount,
                'business_type' => $businessType,
                'business_id' => $businessId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分使用确认成功',
                'data' => ['transaction_id' => $transaction->id]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('积分使用确认失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '积分使用确认失败',
                'data' => []
            ];
        }
    }

    /**
     * 返还积分
     * 第2D1阶段：新增方法
     */
    public function refundPoints(int $userId, float $amount, string $businessType, int $businessId): array
    {
        try {
            // 验证业务类型有效性
            if (!$this->isValidBusinessType($businessType)) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '无效的业务类型',
                    'data' => []
                ];
            }

            DB::beginTransaction();

            $user = User::findOrFail($userId);

            // 验证业务ID是否存在（检查是否有相关的交易记录）
            $existingTransaction = PointsTransaction::where('business_id', $businessId)
                ->where('business_type', $businessType)
                ->where('user_id', $userId)
                ->first();

            Log::info('积分返还业务ID验证', [
                'business_id' => $businessId,
                'business_type' => $businessType,
                'user_id' => $userId,
                'existing_transaction' => $existingTransaction ? $existingTransaction->id : null
            ]);

            if (!$existingTransaction) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '相关业务记录不存在',
                    'data' => []
                ];
            }

            // 创建积分返还记录
            $transaction = PointsTransaction::create([
                'user_id' => $userId,
                'business_type' => $businessType,
                'business_id' => $businessId,
                'amount' => $amount,
                'status' => PointsTransaction::STATUS_SUCCESS,
                'completed_at' => Carbon::now()
            ]);

            // 返还用户积分
            $user->points += $amount;
            $user->save();

            DB::commit();

            Log::info('积分返还成功', [
                'user_id' => $userId,
                'amount' => $amount,
                'business_type' => $businessType,
                'business_id' => $businessId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分返还成功',
                'data' => [
                    'transaction_id' => $transaction->id,
                    'refunded_amount' => $amount,
                    'current_balance' => $user->points
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('积分返还失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '积分返还失败',
                'data' => []
            ];
        }
    }

    /**
     * 通过冻结ID返还积分 (符合dev-api-guidelines-add.mdc标准)
     * @param int $userId
     * @param string $freezeId
     * @param string $returnReason
     * @return array
     */
    public function refundPointsByFreezeId(int $userId, string $freezeId, string $returnReason): array
    {
        try {
            DB::beginTransaction();

            // 查找冻结记录
            $freezeRecord = DB::table('points_freeze')
                ->where('id', $freezeId)
                ->where('user_id', $userId)
                ->first();

            if (!$freezeRecord) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '找不到对应的业务记录',
                    'data' => []
                ];
            }

            // 检查冻结状态：只有frozen状态的记录可以返还
            if ($freezeRecord->status !== 'frozen') {
                $statusMessage = $freezeRecord->status === 'consumed' ? '积分已经被扣除' : '积分已经被返还或扣除';
                return [
                    'code' => ApiCodeEnum::CONFLICT,
                    'message' => $statusMessage,
                    'data' => []
                ];
            }

            $user = User::findOrFail($userId);

            // 更新冻结记录状态为released（已释放）
            DB::table('points_freeze')
                ->where('id', $freezeId)
                ->update([
                    'status' => 'released',
                    'released_at' => Carbon::now(),
                    'reason' => $returnReason,
                    'updated_at' => Carbon::now()
                ]);

            // 返还积分：从冻结积分转回可用积分
            $user->points += $freezeRecord->amount;
            $user->frozen_points -= $freezeRecord->amount;
            $user->save();

            // 创建积分返还交易记录
            $transaction = PointsTransaction::create([
                'user_id' => $userId,
                'business_type' => 'refund',
                'business_id' => $freezeRecord->business_id,
                'amount' => $freezeRecord->amount,
                'status' => PointsTransaction::STATUS_SUCCESS,
                'completed_at' => Carbon::now()
            ]);

            DB::commit();

            Log::info('积分返还成功', [
                'user_id' => $userId,
                'freeze_id' => $freezeId,
                'amount' => $freezeRecord->amount,
                'return_reason' => $returnReason
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分返还成功',
                'data' => [
                    'transaction_id' => $transaction->id,
                    'refunded_amount' => $freezeRecord->amount,
                    'current_balance' => $user->points,
                    'freeze_id' => $freezeId,
                    'return_reason' => $returnReason
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('积分返还失败', [
                'user_id' => $userId,
                'freeze_id' => $freezeId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '积分返还失败',
                'data' => []
            ];
        }
    }
}
