<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use App\Helpers\ApiTokenHelper;
use App\Exceptions\ApiException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
//use Illuminate\Support\Facades\Cache;

class AuthService
{
    /**
     * @param string $username
     * @param string $password
     * @return array
     */
    public function register(string $username, string $password)
    {
        // 检查用户名是否已存在（只检查用户名，不检查密码）
        $existingUser = DB::table('users')->select('id')
            ->where('username', $username)
            ->first();

        if (!empty($existingUser)) {
            $code = ApiCodeEnum::USER_ALREADY_EXISTS;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code, 'message' => $message, 'data' => []];
        }

        try {
            // 使用事务确保数据一致性
            DB::beginTransaction();

            $id = DB::table('users')->insertGetId([
                'username' => $username,
                'password' => $password,
                'points' => 0.00,
                'frozen_points' => 0.00,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            $token = ApiTokenHelper::generateToken($id);
            $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);
            Redis::setex('user:token:'.$id, $ttl, ApiTokenHelper::encryptToken($token));

            DB::commit();

            $code = ApiCodeEnum::SUCCESS;
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => ApiCodeEnum::getDescription($code),
                'data' => [
                    'id' => $id,
                    'token' => $token
                ]
            ];
        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();

            // 处理唯一约束冲突
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $code = ApiCodeEnum::USER_ALREADY_EXISTS;
                $message = ApiCodeEnum::getDescription($code);
                return ['code' => $code, 'message' => $message, 'data' => []];
            }

            // 其他数据库错误
            $code = ApiCodeEnum::SYSTEM_ERROR;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code, 'message' => $message, 'data' => []];
        } catch (\Exception $e) {
            DB::rollBack();

            // 系统错误
            $code = ApiCodeEnum::SYSTEM_ERROR;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code, 'message' => $message, 'data' => []];
        }
    }

    /**
     * @param int $username
     * @param int $password
     * @return array
     */
    public function login(string $username, string $password)
    {
        $user = DB::table('users')->select('id')
            ->where('username', $username)
            ->where('password', $password)
            ->first();
        if(empty($user))
        {
            $code = ApiCodeEnum::USER_NOT_REGISTERED;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code,'message' => $message,'data' => []];
        }else{
            $id = $user->id;
            $code = ApiCodeEnum::SUCCESS;
            $token = ApiTokenHelper::generateToken($id);
            $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);

            $redisKey = 'user:token:'.$id;
            $encryptedToken = ApiTokenHelper::encryptToken($token);
            Redis::setex($redisKey, $ttl, $encryptedToken);

            return ['code' => $code, 'message' => 'success', 'data' => ['token' => $token, 'user' => ['id' => $id]]];
        }
    }

    /**
     * 通过邮箱登录 (符合dev-api-guidelines-add.mdc标准)
     * @param string $email
     * @param string $password
     * @return array
     */
    public function loginByEmail(string $email, string $password)
    {
        $user = DB::table('users')->select('id', 'username', 'email', 'nickname')
            ->where('email', $email)
            ->where('password', $password)
            ->where('status', 1)
            ->first();

        if(empty($user))
        {
            $code = ApiCodeEnum::USER_NOT_REGISTERED;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code,'message' => $message,'data' => []];
        }else{
            $id = $user->id;
            $code = ApiCodeEnum::SUCCESS;
            $token = ApiTokenHelper::generateToken($id);
            $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);

            $redisKey = 'user:token:'.$id;
            $encryptedToken = ApiTokenHelper::encryptToken($token);
            Redis::setex($redisKey, $ttl, $encryptedToken);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $id,
                        'username' => $user->username,
                        'email' => $user->email,
                        'nickname' => $user->nickname
                    ]
                ]
            ];
        }
    }

    /**
     * @param $token
     * @return int
     */
    public function getAuthUserID($token)
    {
        $id = ApiTokenHelper::getUserIdByToken($token);
        if(!empty($id))
        {
            $encrypt_token = Redis::get('user:token:'.$id);
            if($encrypt_token == ApiTokenHelper::encryptToken($token))
            {
                return $id;
            }
        }
        throw new ApiException('Token无效,请从新登录',ApiCodeEnum::INVALID_TOKEN);
    }

    /**
     * 从请求中提取token
     *
     * @param Request $request
     * @return string|null
     */
    public static function extractToken(Request $request): ?string
    {
        // 首先尝试从请求参数中获取token
        $token = $request->input('token');

        if (empty($token)) {
            // 从Authorization头中提取token
            $header = $request->header('Authorization', '');
            $position = strrpos($header, 'Bearer ');
            if ($position !== false) {
                $header = substr($header, $position + 7);
                $token = strpos($header, ',') !== false ? strstr($header, ',', true) : $header;
            }
        }

        return $token ?: null;
    }

    /**
     * 验证token并返回用户信息
     *
     * @param string $token
     * @return object|null 返回用户信息对象或null
     */
    public static function validateToken(string $token): ?object
    {
        // 解析用户ID
        $userId = ApiTokenHelper::getUserIdByToken($token);
        if (empty($userId)) {
            return null;
        }

        // 验证token是否有效 - 使用与存储时相同的键格式
        $redisKey = 'user:token:' . $userId;
        $encryptToken = Redis::get($redisKey);
        if ($encryptToken !== ApiTokenHelper::encryptToken($token)) {
            return null;
        }

        // 查询用户信息
        $user = DB::table('users')->where('id', $userId)->first();
        if (!$user) {
            return null;
        }

        return $user;
    }

    /**
     * 认证用户并返回用户信息或错误响应
     *
     * @param Request $request
     * @return array 返回 ['success' => bool, 'user' => object|null, 'response' => array|null]
     */
    public static function authenticate(Request $request): array
    {
        // 提取token
        $token = self::extractToken($request);
        if (empty($token)) {
            return [
                'success' => false,
                'user' => null,
                'response' => [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '请登录后操作',
                    'data' => []
                ]
            ];
        }

        // 验证token
        $user = self::validateToken($token);
        if (!$user) {
            return [
                'success' => false,
                'user' => null,
                'response' => [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '请登录后操作',
                    'data' => []
                ]
            ];
        }

        return [
            'success' => true,
            'user' => $user,
            'response' => null
        ];
    }

    /**
     * 快速认证方法，直接返回用户信息或抛出认证失败响应
     *
     * @param Request $request
     * @return object 用户信息对象
     * @throws \Exception 认证失败时抛出异常，包含响应数据
     */
    public static function requireAuth(Request $request): object
    {
        $result = self::authenticate($request);

        if (!$result['success']) {
            throw new \Exception(json_encode($result['response']));
        }

        return $result['user'];
    }

    /**
     * 用户登出 - 简化版本
     *
     * @param int $userId 用户ID
     * @return array
     */
    public function logout(int $userId): array
    {
        try {
            // 清除用户Token
            $redisKey = "user:token:{$userId}";
            Redis::del($redisKey);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '登出成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            Log::error('Logout failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '登出失败，请重试',
                'data' => []
            ];
        }
    }

    /**
     * 忘记密码 - 简化版本
     *
     * @param string $email
     * @return array
     */
    public function forgotPassword(string $email): array
    {
        try {
            // 检查用户是否存在
            $user = DB::table('users')->where('email', $email)->first();
            if (!$user) {
                return [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '用户不存在',
                    'data' => []
                ];
            }

            // 生成重置令牌
            $token = bin2hex(random_bytes(32));

            // 存储重置令牌（有效期1小时）
            $resetKey = "password_reset:{$token}";
            Redis::setex($resetKey, 3600, $user->id);

            // 这里应该发送邮件，简化处理只返回成功
            Log::info('Password reset requested', [
                'email' => $email,
                'user_id' => $user->id,
                'token' => $token
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '密码重置邮件发送成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            Log::error('Forgot password failed', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '操作失败，请重试',
                'data' => []
            ];
        }
    }

    /**
     * 重置密码 - 简化版本
     *
     * @param string $token
     * @param string $newPassword
     * @return array
     */
    public function resetPassword(string $token, string $newPassword): array
    {
        try {
            // 验证重置令牌
            $resetKey = "password_reset:{$token}";
            $userId = Redis::get($resetKey);

            if (!$userId) {
                return [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '重置令牌无效或已过期',
                    'data' => []
                ];
            }

            // 更新密码
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            DB::table('users')
                ->where('id', $userId)
                ->update(['password' => $hashedPassword]);

            // 删除重置令牌
            Redis::del($resetKey);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '密码重置成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            Log::error('Reset password failed', [
                'token' => substr($token, 0, 10) . '...',
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '密码重置失败，请重试',
                'data' => []
            ];
        }
    }

    /**
     * Token刷新 - 基于七重同步铁律实施
     *
     * @param string $refreshToken refresh_token
     * @return array
     */
    public function refreshToken(string $refreshToken): array
    {
        try {
            // 1. 解析refresh_token获取用户ID
            $userId = $this->getUserIdFromRefreshToken($refreshToken);
            if (!$userId) {
                return [
                    'code' => 4011, // REFRESH_TOKEN_INVALID
                    'message' => 'refresh_token无效',
                    'data' => []
                ];
            }

            // 2. 验证refresh_token有效性
            if (!$this->validateRefreshToken($refreshToken, $userId)) {
                return [
                    'code' => 4012, // REFRESH_TOKEN_EXPIRED
                    'message' => 'refresh_token过期',
                    'data' => []
                ];
            }

            // 3. 检查Token黑名单
            if ($this->isRefreshTokenBlacklisted($refreshToken)) {
                return [
                    'code' => 4013, // REFRESH_TOKEN_BLACKLISTED
                    'message' => 'refresh_token已失效',
                    'data' => []
                ];
            }

            // 4. 验证用户是否存在
            $user = DB::table('users')->where('id', $userId)->first();
            if (!$user) {
                return [
                    'code' => 4014, // REFRESH_USER_NOT_FOUND
                    'message' => '用户不存在',
                    'data' => []
                ];
            }

            // 5. 生成新的Token对
            $newAccessToken = ApiTokenHelper::generateToken($userId);
            $newRefreshToken = $this->generateRefreshToken($userId);

            // 6. 原子性更新Token
            $this->atomicTokenUpdate($userId, $newAccessToken, $newRefreshToken, $refreshToken);

            // 7. 记录刷新日志
            $this->logTokenRefresh($userId, $refreshToken, $newRefreshToken);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'Token刷新成功',
                'data' => [
                    'access_token' => $newAccessToken,
                    'refresh_token' => $newRefreshToken,
                    'token_type' => 'Bearer',
                    'expires_in' => 3600 * 24 * 30, // 30天
                    'refresh_time' => date('c') // ISO 8601格式
                ]
            ];

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('Token refresh failed', [
                'refresh_token' => substr($refreshToken, 0, 10) . '...',
                'error' => $e->getMessage()
            ]);

            return [
                'code' => 5012, // REFRESH_SYSTEM_ERROR
                'message' => 'Token刷新失败，请重试',
                'data' => []
            ];
        }
    }

    /**
     * 从refresh_token中解析用户ID
     *
     * @param string $refreshToken
     * @return int|null
     */
    private function getUserIdFromRefreshToken(string $refreshToken): ?int
    {
        // 简化实现：从refresh_token中解析用户ID
        // 实际项目中可能需要更复杂的解析逻辑
        try {
            // 假设refresh_token格式为: userId_timestamp_randomString
            $parts = explode('_', $refreshToken);
            if (count($parts) >= 3 && is_numeric($parts[0])) {
                return (int)$parts[0];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to parse user ID from refresh token', [
                'refresh_token' => substr($refreshToken, 0, 10) . '...',
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * 验证refresh_token有效性
     *
     * @param string $refreshToken
     * @param int $userId
     * @return bool
     */
    private function validateRefreshToken(string $refreshToken, int $userId): bool
    {
        $refreshTokenKey = "user:refresh_token:{$userId}";
        $storedToken = Redis::get($refreshTokenKey);

        if (!$storedToken) {
            return false;
        }

        // 解密并比较
        return $this->decryptRefreshToken($storedToken) === $refreshToken;
    }

    /**
     * 检查refresh_token是否在黑名单中
     *
     * @param string $refreshToken
     * @return bool
     */
    private function isRefreshTokenBlacklisted(string $refreshToken): bool
    {
        $blacklistKey = "refresh_token:blacklist:{$refreshToken}";
        return Redis::exists($blacklistKey);
    }

    /**
     * 生成refresh_token
     *
     * @param int $userId
     * @return string
     */
    private function generateRefreshToken(int $userId): string
    {
        $timestamp = time();
        $randomString = bin2hex(random_bytes(16));
        return "{$userId}_{$timestamp}_{$randomString}";
    }

    /**
     * 原子性Token更新
     *
     * @param int $userId
     * @param string $newAccessToken
     * @param string $newRefreshToken
     * @param string $oldRefreshToken
     */
    private function atomicTokenUpdate(int $userId, string $newAccessToken, string $newRefreshToken, string $oldRefreshToken): void
    {
        Redis::multi();

        // 更新access_token
        $accessTokenKey = "user:token:{$userId}";
        $accessTokenTtl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);
        Redis::setex($accessTokenKey, $accessTokenTtl, ApiTokenHelper::encryptToken($newAccessToken));

        // 更新refresh_token
        $refreshTokenKey = "user:refresh_token:{$userId}";
        $refreshTokenTtl = 3600 * 24 * 60; // 60天
        Redis::setex($refreshTokenKey, $refreshTokenTtl, $this->encryptRefreshToken($newRefreshToken));

        // 将旧refresh_token加入黑名单
        $blacklistKey = "refresh_token:blacklist:{$oldRefreshToken}";
        Redis::setex($blacklistKey, $refreshTokenTtl, 'used');

        Redis::exec();
    }

    /**
     * 加密refresh_token
     *
     * @param string $refreshToken
     * @return string
     */
    private function encryptRefreshToken(string $refreshToken): string
    {
        // 使用与access_token相同的加密方法
        return ApiTokenHelper::encryptToken($refreshToken);
    }

    /**
     * 解密refresh_token
     *
     * @param string $encryptedRefreshToken
     * @return string
     */
    private function decryptRefreshToken(string $encryptedRefreshToken): string
    {
        // 简化实现：实际项目中需要实现对应的解密方法
        // 这里假设加密是可逆的
        return $encryptedRefreshToken; // 临时实现
    }

    /**
     * 记录Token刷新日志
     *
     * @param int $userId
     * @param string $oldRefreshToken
     * @param string $newRefreshToken
     */
    private function logTokenRefresh(int $userId, string $oldRefreshToken, string $newRefreshToken): void
    {
        try {
            $logData = [
                'user_id' => $userId,
                'old_refresh_token' => substr($oldRefreshToken, 0, 10) . '...',
                'new_refresh_token' => substr($newRefreshToken, 0, 10) . '...',
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'refresh_time' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 记录到Laravel日志
            Log::info('Token refresh', $logData);

        } catch (\Exception $e) {
            Log::error('Failed to log token refresh', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

}
